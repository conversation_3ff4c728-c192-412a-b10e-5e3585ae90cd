# 小米电视应用测试验证指南

## 测试前准备

### 1. 小米电视设置
1. **开启开发者模式**
   - 进入设置 → 关于 → 连续点击"版本号"7次
   - 返回设置，找到"开发者选项"

2. **允许USB调试**
   - 开发者选项 → 开启"USB调试"
   - 开发者选项 → 开启"USB安装"

3. **允许未知来源应用**
   - 设置 → 安全 → 开启"未知来源"

### 2. 网络连接测试
在电视浏览器中访问目标网址，确保可以正常加载：
```
http://118.31.72.29:8100/#/de-link/BdGpvdNr
```

## 构建测试

### 1. 环境验证
运行环境设置脚本：
```powershell
# 以管理员权限运行
.\setup-environment.ps1
```

### 2. 构建验证
```bash
# 检查Cordova版本
cordova --version

# 检查平台
cordova platform list

# 准备构建
cordova prepare android

# 构建调试版
cordova build android --debug --verbose
```

### 3. 构建成功标志
- 无错误信息输出
- 生成APK文件：`platforms/android/app/build/outputs/apk/debug/app-debug.apk`
- APK文件大小合理（通常2-10MB）

## 安装测试

### 方法1：ADB安装测试
```bash
# 连接电视（确保电视和电脑在同一网络）
adb connect [电视IP地址]:5555

# 检查连接
adb devices

# 安装APK
adb install platforms/android/app/build/outputs/apk/debug/app-debug.apk

# 启动应用
adb shell am start -n com.xiaomi.tvwebapp/.MainActivity
```

### 方法2：U盘安装测试
1. 将APK复制到U盘根目录
2. U盘插入电视USB接口
3. 电视文件管理器中找到APK
4. 点击安装

## 功能测试清单

### 基础功能测试
- [ ] 应用能正常安装
- [ ] 应用图标显示正确
- [ ] 应用能正常启动
- [ ] 启动画面显示正常
- [ ] 网页能正常加载

### 显示测试
- [ ] 应用全屏显示
- [ ] 横屏模式正确
- [ ] 网页内容完整显示
- [ ] 无黑边或变形
- [ ] 字体大小适中

### 交互测试
- [ ] 遥控器方向键可以导航
- [ ] 确认键可以点击
- [ ] 返回键功能正常
- [ ] 菜单键响应正确
- [ ] 焦点指示清晰可见

### 网络测试
- [ ] 网页加载速度正常
- [ ] 网络断开时显示错误提示
- [ ] 重新连接后可以重新加载
- [ ] 重试按钮功能正常

### 稳定性测试
- [ ] 长时间运行无崩溃
- [ ] 内存使用正常
- [ ] 切换应用后能正常返回
- [ ] 电视休眠唤醒后正常

## 性能测试

### 启动时间测试
- 冷启动时间：< 5秒
- 网页加载时间：< 10秒
- 总体响应时间：< 3秒

### 内存使用测试
```bash
# 查看应用内存使用
adb shell dumpsys meminfo com.xiaomi.tvwebapp
```

### CPU使用测试
```bash
# 查看CPU使用情况
adb shell top | grep tvwebapp
```

## 调试方法

### 1. Chrome远程调试
1. 电视连接ADB
2. Chrome浏览器访问：`chrome://inspect`
3. 找到应用WebView进行调试

### 2. 日志查看
```bash
# 查看应用日志
adb logcat | grep -i tvwebapp

# 查看Cordova日志
adb logcat | grep -i cordova

# 查看WebView日志
adb logcat | grep -i chromium
```

### 3. 网络调试
```bash
# 查看网络连接
adb shell netstat | grep 8100

# 测试网络连通性
adb shell ping 118.31.72.29
```

## 常见问题解决

### 安装问题
1. **安装失败**
   - 检查是否开启"未知来源"
   - 确认APK文件完整
   - 尝试卸载旧版本

2. **权限问题**
   - 检查网络权限
   - 确认存储权限

### 显示问题
1. **网页显示异常**
   - 检查网络连接
   - 确认URL正确性
   - 查看WebView版本

2. **全屏问题**
   - 检查config.xml配置
   - 确认CSS样式设置

### 性能问题
1. **加载缓慢**
   - 检查网络速度
   - 优化网页资源
   - 增加缓存策略

2. **内存不足**
   - 清理电视缓存
   - 关闭其他应用
   - 重启电视

## 测试报告模板

### 测试环境
- 电视型号：
- MIUI TV版本：
- 网络环境：
- 测试日期：

### 测试结果
- 安装测试：✅/❌
- 功能测试：✅/❌
- 性能测试：✅/❌
- 稳定性测试：✅/❌

### 发现问题
1. 问题描述
2. 重现步骤
3. 预期结果
4. 实际结果
5. 严重程度

### 测试结论
- [ ] 通过测试，可以发布
- [ ] 存在问题，需要修复
- [ ] 需要进一步测试

## 发布前检查

### 最终验证
- [ ] 所有测试用例通过
- [ ] 性能指标达标
- [ ] 用户体验良好
- [ ] 无严重Bug
- [ ] 文档完整

### 发布准备
- [ ] 生成发布版APK
- [ ] 签名APK（如需要）
- [ ] 准备安装说明
- [ ] 备份项目文件
