# 快速生成APK指南

## 🚀 方法1：自动安装环境（推荐）

### 1. 下载并安装Java JDK
```powershell
# 下载 OpenJDK 11（推荐）
# 访问：https://adoptium.net/temurin/releases/
# 选择：OpenJDK 11 (LTS) -> Windows -> x64 -> JDK -> .msi

# 或使用 Chocolatey 自动安装
choco install openjdk11
```

### 2. 下载并安装Android Studio
```powershell
# 访问：https://developer.android.com/studio
# 下载 Android Studio 并安装
# 安装时选择 "Standard" 安装类型
```

### 3. 设置环境变量
```powershell
# 设置 JAVA_HOME
$env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"

# 设置 ANDROID_HOME
$env:ANDROID_HOME = "$env:LOCALAPPDATA\Android\Sdk"
$env:ANDROID_SDK_ROOT = $env:ANDROID_HOME

# 添加到 PATH
$env:PATH += ";$env:JAVA_HOME\bin"
$env:PATH += ";$env:ANDROID_HOME\platform-tools"
$env:PATH += ";$env:ANDROID_HOME\tools"
```

### 4. 构建APK
```powershell
# 进入项目目录
cd tvapp

# 运行构建脚本
.\build.ps1

# 或手动构建
npx cordova build android
```

## 🚀 方法2：使用在线构建服务

### PhoneGap Build（已停服）
PhoneGap Build 服务已停止，但可以考虑其他替代方案。

### GitHub Actions 自动构建
我可以为您创建 GitHub Actions 配置文件，在云端自动构建APK。

## 🚀 方法3：使用预配置的开发环境

### 使用 Docker
```dockerfile
# 创建 Dockerfile
FROM beevelop/cordova:latest

WORKDIR /app
COPY . .

RUN npm install
RUN cordova build android
```

## 🚀 方法4：最快速解决方案

如果您急需APK文件，建议：

### 1. 安装 Chocolatey（Windows包管理器）
```powershell
# 以管理员权限运行 PowerShell
Set-ExecutionPolicy Bypass -Scope Process -Force
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
```

### 2. 一键安装所有依赖
```powershell
# 安装 Java JDK
choco install openjdk11 -y

# 安装 Android SDK
choco install android-sdk -y

# 刷新环境变量
refreshenv
```

### 3. 设置 Android SDK
```powershell
# 下载必要的 Android SDK 组件
sdkmanager "platform-tools" "platforms;android-28" "build-tools;28.0.3"
```

### 4. 构建APK
```powershell
cd tvapp
npx cordova build android
```

## 📱 APK文件位置

构建成功后，APK文件将位于：
```
platforms/android/app/build/outputs/apk/debug/app-debug.apk
```

## 🔧 故障排除

### 常见错误1：JAVA_HOME 未设置
```powershell
# 检查 Java 安装
java -version

# 设置 JAVA_HOME
$env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"
```

### 常见错误2：Android SDK 未找到
```powershell
# 检查 Android SDK
$env:ANDROID_HOME = "$env:LOCALAPPDATA\Android\Sdk"

# 或手动设置路径
$env:ANDROID_HOME = "C:\Android\Sdk"
```

### 常见错误3：构建工具版本问题
```powershell
# 安装特定版本的构建工具
sdkmanager "build-tools;28.0.3"
```

## ⚡ 超快速方案（5分钟内完成）

如果您有管理员权限，运行以下命令：

```powershell
# 1. 安装 Chocolatey
Set-ExecutionPolicy Bypass -Scope Process -Force; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# 2. 安装依赖
choco install openjdk11 android-sdk nodejs -y

# 3. 刷新环境
refreshenv

# 4. 设置环境变量
$env:JAVA_HOME = "C:\Program Files\OpenJDK\openjdk-11.0.2"
$env:ANDROID_HOME = "C:\Android\android-sdk"
$env:PATH += ";$env:JAVA_HOME\bin;$env:ANDROID_HOME\platform-tools"

# 5. 安装 Android 组件
sdkmanager "platform-tools" "platforms;android-28" "build-tools;28.0.3"

# 6. 构建APK
cd tvapp
npm install -g cordova
npx cordova build android
```

## 📞 需要帮助？

如果遇到问题，请告诉我：
1. 错误信息的完整内容
2. 您的操作系统版本
3. 是否有管理员权限

我会为您提供针对性的解决方案！
