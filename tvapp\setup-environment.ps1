# 小米电视应用开发环境设置脚本
# 请以管理员权限运行此脚本

Write-Host "=== 小米电视应用开发环境设置 ===" -ForegroundColor Green

# 检查是否以管理员权限运行
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "请以管理员权限运行此脚本！" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 1. 检查Node.js
Write-Host "检查Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "Node.js已安装: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "Node.js未安装，请从 https://nodejs.org/ 下载安装" -ForegroundColor Red
    $install = Read-Host "是否现在打开下载页面? (y/n)"
    if ($install -eq "y") {
        Start-Process "https://nodejs.org/"
    }
}

# 2. 检查Java
Write-Host "检查Java..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1
    Write-Host "Java已安装" -ForegroundColor Green
} catch {
    Write-Host "Java未安装，请从 https://adoptium.net/ 下载安装JDK 11" -ForegroundColor Red
    $install = Read-Host "是否现在打开下载页面? (y/n)"
    if ($install -eq "y") {
        Start-Process "https://adoptium.net/"
    }
}

# 3. 检查Android SDK
Write-Host "检查Android SDK..." -ForegroundColor Yellow
if ($env:ANDROID_HOME) {
    Write-Host "ANDROID_HOME已设置: $env:ANDROID_HOME" -ForegroundColor Green
} else {
    Write-Host "ANDROID_HOME未设置，请安装Android Studio或Android SDK" -ForegroundColor Red
    $install = Read-Host "是否现在打开Android Studio下载页面? (y/n)"
    if ($install -eq "y") {
        Start-Process "https://developer.android.com/studio"
    }
}

# 4. 安装Cordova CLI
Write-Host "安装Cordova CLI..." -ForegroundColor Yellow
try {
    npm install -g cordova
    Write-Host "Cordova CLI安装完成" -ForegroundColor Green
} catch {
    Write-Host "Cordova CLI安装失败，请检查网络连接" -ForegroundColor Red
}

# 5. 检查项目依赖
Write-Host "安装项目依赖..." -ForegroundColor Yellow
if (Test-Path "package.json") {
    npm install
    Write-Host "项目依赖安装完成" -ForegroundColor Green
} else {
    Write-Host "未找到package.json文件，请确保在项目根目录运行此脚本" -ForegroundColor Red
}

Write-Host "=== 环境设置完成 ===" -ForegroundColor Green
Write-Host "请重启PowerShell窗口以确保环境变量生效" -ForegroundColor Yellow
Write-Host "然后运行以下命令构建APK:" -ForegroundColor Cyan
Write-Host "  cordova build android" -ForegroundColor White

Read-Host "按任意键退出"
