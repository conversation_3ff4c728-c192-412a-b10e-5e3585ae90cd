# 小米电视MIUI TV网页应用

专为小米电视MIUI TV 1.3.51设计的全屏网页应用，使用Apache Cordova框架开发。

## 🎯 项目特点

- **全屏显示**：应用启动时自动进入全屏模式，完美适配电视屏幕
- **横屏优化**：强制横屏显示，符合电视使用习惯
- **遥控器支持**：完整支持电视遥控器导航和操作
- **网络优化**：智能网络检测和错误处理机制
- **电视启动器**：支持从电视主界面直接启动

## 📱 目标网页

应用将全屏显示以下网页：
```
http://118.31.72.29:8100/#/de-link/BdGpvdNr
```

## 🚀 快速开始

### 环境准备
1. 运行环境设置脚本（需要管理员权限）：
   ```powershell
   .\setup-environment.ps1
   ```

2. 或手动安装以下软件：
   - Node.js (14+)
   - Java JDK (8 或 11)
   - Android Studio 或 Android SDK
   - Cordova CLI

### 构建应用
使用快速构建脚本：
```powershell
# 构建调试版
.\build.ps1

# 构建发布版
.\build.ps1 -BuildType release

# 清理后构建
.\build.ps1 -Clean

# 构建并安装到设备
.\build.ps1 -Install -DeviceIP *************
```

或使用传统命令：
```bash
# 安装依赖
npm install

# 构建APK
npm run build

# 构建发布版
npm run build:release
```

## 📦 安装到小米电视

### 方法1：U盘安装
1. 将生成的APK文件复制到U盘
2. U盘插入小米电视USB接口
3. 在电视文件管理器中找到APK文件
4. 点击安装

### 方法2：ADB安装
```bash
# 连接电视（确保在同一网络）
adb connect [电视IP地址]:5555

# 安装APK
adb install platforms/android/app/build/outputs/apk/debug/app-debug.apk
```

## 🔧 项目结构

```
tvapp/
├── www/                    # 网页资源
│   ├── index.html         # 主页面
│   ├── css/               # 样式文件
│   ├── js/                # JavaScript文件
│   └── res/               # 图标和启动画面
├── platforms/             # 平台特定代码
│   └── android/           # Android平台
├── config.xml             # Cordova配置文件
├── package.json           # 项目配置
├── build.ps1              # 快速构建脚本
├── setup-environment.ps1  # 环境设置脚本
├── BUILD_GUIDE.md         # 详细构建指南
├── TESTING_GUIDE.md       # 测试验证指南
└── README.md              # 项目说明
```

## ⚙️ 配置说明

### 应用配置 (config.xml)
- **应用ID**: `com.xiaomi.tvwebapp`
- **应用名称**: 小米电视网页应用
- **目标API**: Android API 28
- **最低API**: Android API 19

### 电视优化配置
- 全屏模式和沉浸式体验
- 横屏锁定
- 电视启动器支持
- 遥控器导航优化
- 硬件加速启用

### 网络配置
- HTTP/HTTPS访问权限
- 跨域请求支持
- 网络状态检测
- 自动重连机制

## 🧪 测试验证

运行完整的测试流程：
```powershell
# 查看测试指南
Get-Content TESTING_GUIDE.md

# 基础功能测试
.\build.ps1 -Install -DeviceIP [电视IP]

# 性能测试
adb shell dumpsys meminfo com.xiaomi.tvwebapp
```

### 测试清单
- [x] 应用正常安装
- [x] 全屏显示正确
- [x] 网页加载正常
- [x] 遥控器导航
- [x] 错误处理机制
- [x] 性能表现良好

## 🛠️ 自定义配置

### 修改目标网页
编辑 `www/index.html` 文件：
```html
<iframe id="mainFrame" src="你的网页URL"></iframe>
```

### 修改应用信息
编辑 `config.xml` 文件：
```xml
<name>你的应用名称</name>
<widget id="com.yourcompany.appname">
```

### 自定义样式
编辑 `www/css/index.css` 或在 `www/index.html` 中修改样式。

## 📋 NPM脚本

```bash
npm run build          # 构建调试版APK
npm run build:release  # 构建发布版APK
npm run prepare        # 准备Android平台
npm run clean          # 清理构建文件
npm run serve          # 启动开发服务器
```

## 🐛 故障排除

### 常见问题
1. **构建失败**: 检查Java和Android SDK环境变量
2. **网页无法加载**: 确认网络连接和URL可访问性
3. **应用无法安装**: 确保电视允许安装未知来源应用

### 调试方法
- Chrome DevTools远程调试
- ADB日志查看：`adb logcat | grep tvwebapp`
- 网络连通性测试

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📞 技术支持

如遇到问题，请查看：
1. [BUILD_GUIDE.md](BUILD_GUIDE.md) - 详细构建指南
2. [TESTING_GUIDE.md](TESTING_GUIDE.md) - 测试验证指南
3. [Cordova官方文档](https://cordova.apache.org/docs/)

---

**注意**: 请确保目标网页服务器允许在iframe中嵌入，并且网络连接稳定。
