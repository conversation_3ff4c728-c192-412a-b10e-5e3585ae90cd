<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <title>小米电视网页应用</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #000;
            font-family: Arial, sans-serif;
        }

        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 24px;
            z-index: 9999;
        }

        #loading.hidden {
            display: none;
        }

        iframe {
            width: 100%;
            height: 100%;
            border: none;
            background-color: #000;
        }

        /* 电视遥控器焦点样式 */
        iframe:focus {
            outline: 2px solid #00ff00;
            outline-offset: -2px;
        }

        /* 错误提示样式 */
        #error {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            display: none;
            z-index: 10000;
        }

        #retry-btn {
            margin-top: 15px;
            padding: 10px 20px;
            background-color: #fff;
            color: #000;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        #retry-btn:focus {
            outline: 2px solid #00ff00;
        }
    </style>
</head>
<body>
    <!-- 加载提示 -->
    <div id="loading">正在加载网页...</div>

    <!-- 错误提示 -->
    <div id="error">
        <div>网页加载失败</div>
        <button id="retry-btn" onclick="retryLoad()">重新加载</button>
    </div>

    <!-- 主要内容 -->
    <iframe id="mainFrame" src="http://************:8100/#/de-link/BdGpvdNr"></iframe>

    <script>
        // 全局变量
        let iframe = null;
        let loading = null;
        let errorDiv = null;
        let loadTimeout = null;

        // 初始化
        document.addEventListener('deviceready', onDeviceReady, false);

        function onDeviceReady() {
            console.log('Device is ready');
            initializeApp();
        }

        // 如果不是Cordova环境，直接初始化
        if (!window.cordova) {
            document.addEventListener('DOMContentLoaded', initializeApp);
        }

        function initializeApp() {
            iframe = document.getElementById('mainFrame');
            loading = document.getElementById('loading');
            errorDiv = document.getElementById('error');

            // 设置加载超时
            loadTimeout = setTimeout(function() {
                showError();
            }, 30000); // 30秒超时

            // iframe加载事件
            iframe.onload = function() {
                console.log('Iframe loaded successfully');
                clearTimeout(loadTimeout);
                hideLoading();
                iframe.focus();
            };

            iframe.onerror = function() {
                console.log('Iframe load error');
                clearTimeout(loadTimeout);
                showError();
            };

            // 处理遥控器按键
            document.addEventListener('keydown', handleKeyDown);

            // 防止页面滚动
            document.addEventListener('touchmove', function(e) {
                e.preventDefault();
            }, { passive: false });

            // 确保iframe始终聚焦
            setInterval(function() {
                if (document.activeElement !== iframe && !errorDiv.style.display) {
                    iframe.focus();
                }
            }, 1000);
        }

        function hideLoading() {
            if (loading) {
                loading.classList.add('hidden');
            }
        }

        function showError() {
            hideLoading();
            if (errorDiv) {
                errorDiv.style.display = 'block';
                document.getElementById('retry-btn').focus();
            }
        }

        function retryLoad() {
            errorDiv.style.display = 'none';
            loading.classList.remove('hidden');
            iframe.src = iframe.src; // 重新加载

            // 重新设置超时
            loadTimeout = setTimeout(function() {
                showError();
            }, 30000);
        }

        // 处理遥控器按键
        function handleKeyDown(event) {
            console.log('Key pressed:', event.keyCode);

            // 如果错误提示显示，处理重试按钮
            if (errorDiv.style.display === 'block') {
                if (event.keyCode === 13 || event.keyCode === 23) { // Enter或确认键
                    retryLoad();
                    return;
                }
            }

            // 常见电视遥控器按键码
            switch(event.keyCode) {
                case 4: // 返回键
                    event.preventDefault();
                    if (window.navigator && window.navigator.app) {
                        navigator.app.exitApp();
                    }
                    break;
                case 82: // 菜单键
                    event.preventDefault();
                    break;
                case 27: // ESC键
                    event.preventDefault();
                    break;
            }
        }

        // 全屏处理
        function enterFullscreen() {
            const element = document.documentElement;
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        }

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden && iframe) {
                iframe.focus();
            }
        });

        // 窗口焦点处理
        window.addEventListener('focus', function() {
            if (iframe) {
                iframe.focus();
            }
        });

        // 错误处理
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
        });

        // 未处理的Promise拒绝
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
        });
    </script>
</body>
</html>
