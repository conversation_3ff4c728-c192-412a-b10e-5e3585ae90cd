# 小米电视应用快速构建脚本
param(
    [string]$BuildType = "debug",
    [switch]$Clean = $false,
    [switch]$Install = $false,
    [string]$DeviceIP = ""
)

Write-Host "=== 小米电视应用构建脚本 ===" -ForegroundColor Green
Write-Host "构建类型: $BuildType" -ForegroundColor Yellow

# 检查环境
function Check-Environment {
    Write-Host "检查构建环境..." -ForegroundColor Yellow
    
    # 检查Node.js
    try {
        $nodeVersion = node --version
        Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ Node.js未安装" -ForegroundColor Red
        return $false
    }
    
    # 检查Cordova
    try {
        $cordovaVersion = npx cordova --version
        Write-Host "✅ Cordova: $cordovaVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ Cordova未安装" -ForegroundColor Red
        return $false
    }
    
    # 检查Java
    try {
        java -version 2>&1 | Out-Null
        Write-Host "✅ Java已安装" -ForegroundColor Green
    } catch {
        Write-Host "❌ Java未安装" -ForegroundColor Red
        return $false
    }
    
    # 检查Android SDK
    if ($env:ANDROID_HOME) {
        Write-Host "✅ Android SDK: $env:ANDROID_HOME" -ForegroundColor Green
    } else {
        Write-Host "❌ ANDROID_HOME未设置" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# 清理构建
function Clean-Build {
    Write-Host "清理构建文件..." -ForegroundColor Yellow
    
    if (Test-Path "platforms/android/app/build") {
        Remove-Item -Recurse -Force "platforms/android/app/build"
        Write-Host "✅ 清理完成" -ForegroundColor Green
    }
}

# 构建应用
function Build-App {
    param([string]$Type)
    
    Write-Host "开始构建应用..." -ForegroundColor Yellow
    
    try {
        # 准备平台
        Write-Host "准备Android平台..." -ForegroundColor Cyan
        npx cordova prepare android
        
        # 构建
        Write-Host "构建APK文件..." -ForegroundColor Cyan
        if ($Type -eq "release") {
            npx cordova build android --release
            $apkPath = "platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk"
        } else {
            npx cordova build android --debug
            $apkPath = "platforms/android/app/build/outputs/apk/debug/app-debug.apk"
        }
        
        # 检查APK文件
        if (Test-Path $apkPath) {
            $apkSize = (Get-Item $apkPath).Length / 1MB
            Write-Host "✅ 构建成功!" -ForegroundColor Green
            Write-Host "📱 APK位置: $apkPath" -ForegroundColor Cyan
            Write-Host "📏 APK大小: $([math]::Round($apkSize, 2)) MB" -ForegroundColor Cyan
            return $apkPath
        } else {
            Write-Host "❌ 构建失败，未找到APK文件" -ForegroundColor Red
            return $null
        }
        
    } catch {
        Write-Host "❌ 构建过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 安装到设备
function Install-ToDevice {
    param([string]$ApkPath, [string]$IP)
    
    Write-Host "安装应用到设备..." -ForegroundColor Yellow
    
    try {
        if ($IP) {
            # 通过IP连接
            Write-Host "连接到设备: $IP" -ForegroundColor Cyan
            adb connect "${IP}:5555"
        }
        
        # 检查设备连接
        $devices = adb devices
        if ($devices -match "device$") {
            Write-Host "✅ 设备已连接" -ForegroundColor Green
            
            # 安装APK
            Write-Host "安装APK..." -ForegroundColor Cyan
            adb install -r $ApkPath
            
            Write-Host "✅ 安装完成!" -ForegroundColor Green
            
            # 启动应用
            $startApp = Read-Host "是否启动应用? (y/n)"
            if ($startApp -eq "y") {
                adb shell am start -n com.xiaomi.tvwebapp/.MainActivity
                Write-Host "✅ 应用已启动" -ForegroundColor Green
            }
            
        } else {
            Write-Host "❌ 未找到连接的设备" -ForegroundColor Red
            Write-Host "请确保:" -ForegroundColor Yellow
            Write-Host "1. 电视开启USB调试" -ForegroundColor White
            Write-Host "2. 电视和电脑在同一网络" -ForegroundColor White
            Write-Host "3. 使用正确的IP地址" -ForegroundColor White
        }
        
    } catch {
        Write-Host "❌ 安装失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 显示帮助信息
function Show-Help {
    Write-Host "使用方法:" -ForegroundColor Cyan
    Write-Host "  .\build.ps1                    # 构建调试版" -ForegroundColor White
    Write-Host "  .\build.ps1 -BuildType release # 构建发布版" -ForegroundColor White
    Write-Host "  .\build.ps1 -Clean             # 清理后构建" -ForegroundColor White
    Write-Host "  .\build.ps1 -Install           # 构建并安装" -ForegroundColor White
    Write-Host "  .\build.ps1 -DeviceIP ************* -Install # 指定设备IP并安装" -ForegroundColor White
    Write-Host ""
    Write-Host "参数说明:" -ForegroundColor Cyan
    Write-Host "  -BuildType: debug 或 release" -ForegroundColor White
    Write-Host "  -Clean: 清理构建文件" -ForegroundColor White
    Write-Host "  -Install: 构建后自动安装到设备" -ForegroundColor White
    Write-Host "  -DeviceIP: 设备IP地址" -ForegroundColor White
}

# 主流程
function Main {
    # 显示帮助
    if ($args -contains "-h" -or $args -contains "--help") {
        Show-Help
        return
    }
    
    # 检查环境
    if (-not (Check-Environment)) {
        Write-Host "❌ 环境检查失败，请先运行 setup-environment.ps1" -ForegroundColor Red
        return
    }
    
    # 清理构建
    if ($Clean) {
        Clean-Build
    }
    
    # 构建应用
    $apkPath = Build-App -Type $BuildType
    
    if ($apkPath) {
        # 安装到设备
        if ($Install) {
            Install-ToDevice -ApkPath $apkPath -IP $DeviceIP
        }
        
        Write-Host ""
        Write-Host "=== 构建完成 ===" -ForegroundColor Green
        Write-Host "APK文件: $apkPath" -ForegroundColor Cyan
        
        if (-not $Install) {
            Write-Host ""
            Write-Host "安装方法:" -ForegroundColor Yellow
            Write-Host "1. USB安装: 复制APK到U盘，在电视上安装" -ForegroundColor White
            Write-Host "2. ADB安装: adb install `"$apkPath`"" -ForegroundColor White
        }
    }
}

# 执行主流程
Main
