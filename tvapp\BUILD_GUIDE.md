# 小米电视MIUI TV 1.3.51 网页应用构建指南

## 项目概述
这是一个专为小米电视MIUI TV 1.3.51设计的全屏网页应用，使用Apache Cordova框架开发。应用将全屏显示指定的网页：http://118.31.72.29:8100/#/de-link/BdGpvdNr

## 环境要求

### 必需软件
1. **Node.js** (版本 14 或更高)
   - 下载地址：https://nodejs.org/
   
2. **Java Development Kit (JDK)** (版本 8 或 11)
   - 下载地址：https://adoptium.net/
   - 设置环境变量 JAVA_HOME
   
3. **Android Studio** 或 **Android SDK**
   - 下载地址：https://developer.android.com/studio
   - 安装 Android SDK Platform-Tools
   - 设置环境变量 ANDROID_HOME 和 ANDROID_SDK_ROOT
   
4. **Gradle** (通常随Android Studio安装)

### 环境变量设置
```bash
# Windows PowerShell
$env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-11.0.x.x-hotspot"
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:ANDROID_SDK_ROOT = $env:ANDROID_HOME
$env:PATH += ";$env:JAVA_HOME\bin;$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools"
```

## 构建步骤

### 1. 安装依赖
```bash
cd tvapp
npm install
```

### 2. 安装Cordova CLI（如果未安装）
```bash
npm install -g cordova
```

### 3. 准备Android平台
```bash
cordova prepare android
```

### 4. 构建调试版APK
```bash
cordova build android
```

### 5. 构建发布版APK（可选）
```bash
cordova build android --release
```

## APK文件位置
构建完成后，APK文件将位于：
- 调试版：`platforms/android/app/build/outputs/apk/debug/app-debug.apk`
- 发布版：`platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk`

## 应用特性

### 电视优化配置
- **全屏模式**：应用启动时自动进入全屏模式
- **横屏锁定**：强制横屏显示，适合电视屏幕
- **遥控器支持**：支持电视遥控器导航
- **电视启动器**：支持从电视主界面启动

### 网络配置
- 允许HTTP和HTTPS访问
- 支持跨域请求
- 网络状态检测

### 用户体验
- 加载提示界面
- 网络错误处理和重试功能
- 自动焦点管理
- 防止意外退出

## 安装到小米电视

### 方法1：USB安装
1. 将APK文件复制到U盘
2. 将U盘插入小米电视
3. 在电视上打开文件管理器
4. 找到APK文件并安装

### 方法2：ADB安装（需要开启开发者模式）
```bash
adb connect [电视IP地址]
adb install app-debug.apk
```

## 故障排除

### 常见问题
1. **构建失败**：检查Java和Android SDK环境变量
2. **网页无法加载**：检查网络连接和URL可访问性
3. **应用无法安装**：确保电视允许安装未知来源应用

### 调试方法
1. 使用Chrome DevTools远程调试
2. 查看Cordova日志：`cordova run android --device --verbose`
3. 使用ADB查看设备日志：`adb logcat`

## 自定义配置

### 修改网页URL
编辑 `www/index.html` 文件中的iframe src属性：
```html
<iframe id="mainFrame" src="你的网页URL"></iframe>
```

### 修改应用信息
编辑 `config.xml` 文件：
- 应用名称：`<name>应用名称</name>`
- 应用ID：`<widget id="com.yourcompany.appname">`
- 版本号：`version="1.0.0"`

## 技术支持
如遇到问题，请检查：
1. Cordova官方文档：https://cordova.apache.org/docs/
2. Android开发者文档：https://developer.android.com/
3. 小米电视开发者文档
