# 🚀 立即生成APK - 最快方法

## ⚡ 方法1：一键自动构建（推荐）

**只需要2步，5-10分钟完成！**

### 第1步：以管理员身份运行PowerShell
1. 按 `Win + X`，选择 "Windows PowerShell (管理员)"
2. 或右键点击开始菜单，选择 "Windows PowerShell (管理员)"

### 第2步：运行自动构建脚本
```powershell
cd "d:\myproject\APK\tvapp"
.\auto-build-apk.ps1
```

脚本会自动：
- ✅ 安装 Java JDK 11
- ✅ 安装 Node.js
- ✅ 安装 Android SDK
- ✅ 配置环境变量
- ✅ 构建 APK 文件

**完成后APK文件位置：`xiaomi-tv-webapp.apk`**

---

## ⚡ 方法2：手动快速安装

如果自动脚本有问题，按以下步骤手动安装：

### 1. 安装 Java JDK
```powershell
# 下载并安装 OpenJDK 11
# 访问：https://adoptium.net/temurin/releases/
# 选择：OpenJDK 11 (LTS) -> Windows -> x64 -> JDK -> .msi
```

### 2. 安装 Android Studio
```powershell
# 访问：https://developer.android.com/studio
# 下载并安装 Android Studio
# 安装时选择 "Standard" 安装
```

### 3. 设置环境变量
```powershell
# 设置 JAVA_HOME（替换为实际安装路径）
$env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"

# 设置 ANDROID_HOME
$env:ANDROID_HOME = "$env:LOCALAPPDATA\Android\Sdk"

# 添加到 PATH
$env:PATH += ";$env:JAVA_HOME\bin;$env:ANDROID_HOME\platform-tools"
```

### 4. 构建 APK
```powershell
cd "d:\myproject\APK\tvapp"
npm install -g cordova
npx cordova build android
```

---

## ⚡ 方法3：使用 Chocolatey（超快）

### 1. 安装 Chocolatey
```powershell
# 以管理员身份运行
Set-ExecutionPolicy Bypass -Scope Process -Force
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
```

### 2. 一键安装所有依赖
```powershell
choco install openjdk11 android-sdk nodejs -y
refreshenv
```

### 3. 设置环境变量
```powershell
$env:JAVA_HOME = "C:\Program Files\OpenJDK\openjdk-11.0.2"
$env:ANDROID_HOME = "C:\Android\android-sdk"
$env:PATH += ";$env:JAVA_HOME\bin;$env:ANDROID_HOME\platform-tools"
```

### 4. 安装 Android 组件
```powershell
sdkmanager "platform-tools" "platforms;android-28" "build-tools;28.0.3"
```

### 5. 构建 APK
```powershell
cd "d:\myproject\APK\tvapp"
npm install -g cordova
npx cordova build android
```

---

## 📱 APK 文件位置

构建成功后，APK 文件位于：
```
platforms\android\app\build\outputs\apk\debug\app-debug.apk
```

自动脚本会复制到根目录：
```
xiaomi-tv-webapp.apk
```

---

## 🔧 常见问题解决

### 问题1：Java 未找到
```powershell
# 检查 Java 安装
java -version

# 如果未安装，下载安装：
# https://adoptium.net/temurin/releases/
```

### 问题2：ANDROID_HOME 未设置
```powershell
# 找到 Android SDK 路径
$env:ANDROID_HOME = "$env:LOCALAPPDATA\Android\Sdk"

# 或手动设置
$env:ANDROID_HOME = "C:\Android\Sdk"
```

### 问题3：构建失败
```powershell
# 清理后重新构建
npx cordova clean android
npx cordova build android --verbose
```

---

## 🎯 验证构建成功

构建成功的标志：
- ✅ 无错误信息
- ✅ 生成 APK 文件
- ✅ APK 文件大小 2-10MB

检查 APK：
```powershell
# 检查文件是否存在
Test-Path "platforms\android\app\build\outputs\apk\debug\app-debug.apk"

# 查看文件大小
Get-Item "platforms\android\app\build\outputs\apk\debug\app-debug.apk" | Select-Object Name, Length
```

---

## 📲 安装到小米电视

### 方法1：U盘安装（推荐）
1. 将 APK 复制到 U盘
2. U盘插入小米电视
3. 电视文件管理器中安装

### 方法2：ADB 网络安装
```powershell
# 连接电视
adb connect [电视IP]:5555

# 安装 APK
adb install xiaomi-tv-webapp.apk
```

---

## 🆘 需要帮助？

如果遇到问题：

1. **运行自动脚本**：`.\auto-build-apk.ps1`
2. **查看详细指南**：`QUICK_APK_BUILD.md`
3. **检查错误日志**：复制完整错误信息
4. **联系支持**：提供错误信息和系统版本

---

## ⏱️ 预计时间

- **自动脚本**：5-10分钟
- **手动安装**：10-20分钟
- **Chocolatey方法**：3-8分钟

**选择最适合您的方法，开始构建APK吧！** 🚀
