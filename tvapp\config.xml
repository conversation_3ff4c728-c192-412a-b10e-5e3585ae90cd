<?xml version='1.0' encoding='utf-8'?>
<widget id="com.xiaomi.tvwebapp" version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <name>小米电视网页应用</name>
    <description>专为小米电视MIUI TV 1.3.51设计的全屏网页应用</description>
    <author email="<EMAIL>" href="https://example.com">
        TV App Developer
    </author>
    <content src="index.html" />

    <!-- 网络访问权限 -->
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-navigation href="http://*/*" />
    <allow-navigation href="https://*/*" />

    <!-- 全局偏好设置 -->
    <preference name="Fullscreen" value="true" />
    <preference name="Orientation" value="landscape" />
    <preference name="DisallowOverscroll" value="true" />
    <preference name="BackgroundColor" value="0x000000" />
    <preference name="HideKeyboardFormAccessoryBar" value="true" />
    <preference name="KeyboardDisplayRequiresUserAction" value="false" />

    <!-- Android平台特定配置 -->
    <platform name="android">
        <!-- 目标API级别适合小米电视 -->
        <preference name="android-minSdkVersion" value="19" />
        <preference name="android-targetSdkVersion" value="28" />
        <preference name="android-compileSdkVersion" value="28" />

        <!-- 全屏和沉浸式模式 -->
        <preference name="Fullscreen" value="true" />
        <preference name="StatusBarOverlaysWebView" value="false" />
        <preference name="StatusBarBackgroundColor" value="#000000" />
        <preference name="NavigationBarBackgroundColor" value="#000000" />

        <!-- 硬件加速和性能优化 -->
        <preference name="AndroidLaunchMode" value="singleTop" />
        <preference name="AndroidWindowSplashScreenAnimatedIcon" value="false" />

        <!-- 电视相关权限 -->
        <uses-permission android:name="android.permission.INTERNET" />
        <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
        <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

        <!-- 电视特性声明 -->
        <uses-feature android:name="android.software.leanback" android:required="false" />
        <uses-feature android:name="android.hardware.touchscreen" android:required="false" />
        <uses-feature android:name="android.hardware.faketouch" android:required="false" />

        <!-- 电视应用类别 -->
        <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application/activity/intent-filter">
            <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
        </edit-config>

        <!-- 全屏和沉浸式模式配置 -->
        <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application/activity">
            <activity android:screenOrientation="landscape"
                     android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
                     android:launchMode="singleTask"
                     android:clearTaskOnLaunch="true" />
        </edit-config>

        <!-- 应用图标和启动画面 -->
        <icon density="ldpi" src="www/res/icon/android/drawable-ldpi-icon.png" />
        <icon density="mdpi" src="www/res/icon/android/drawable-mdpi-icon.png" />
        <icon density="hdpi" src="www/res/icon/android/drawable-hdpi-icon.png" />
        <icon density="xhdpi" src="www/res/icon/android/drawable-xhdpi-icon.png" />
        <icon density="xxhdpi" src="www/res/icon/android/drawable-xxhdpi-icon.png" />
        <icon density="xxxhdpi" src="www/res/icon/android/drawable-xxxhdpi-icon.png" />

        <!-- 启动画面配置 -->
        <preference name="AndroidWindowSplashScreenAnimatedIcon" value="www/res/icon/android/drawable-hdpi-icon.png" />
        <preference name="AndroidWindowSplashScreenBackground" value="#000000" />
        <preference name="SplashScreenDelay" value="3000" />
        <preference name="SplashMaintainAspectRatio" value="true" />
        <preference name="SplashShowOnlyFirstTime" value="false" />
    </platform>
</widget>
