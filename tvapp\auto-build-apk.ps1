# 自动安装环境并构建APK脚本
# 需要管理员权限运行

param(
    [switch]$SkipInstall = $false,
    [switch]$QuickInstall = $false
)

Write-Host "=== 小米电视应用自动构建脚本 ===" -ForegroundColor Green

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 需要管理员权限运行此脚本！" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 检查现有环境
function Test-Environment {
    $javaOk = $false
    $androidOk = $false
    $nodeOk = $false
    
    try {
        java -version 2>&1 | Out-Null
        $javaOk = $true
        Write-Host "✅ Java 已安装" -ForegroundColor Green
    } catch {
        Write-Host "❌ Java 未安装" -ForegroundColor Red
    }
    
    try {
        node --version | Out-Null
        $nodeOk = $true
        Write-Host "✅ Node.js 已安装" -ForegroundColor Green
    } catch {
        Write-Host "❌ Node.js 未安装" -ForegroundColor Red
    }
    
    if ($env:ANDROID_HOME -and (Test-Path $env:ANDROID_HOME)) {
        $androidOk = $true
        Write-Host "✅ Android SDK 已配置" -ForegroundColor Green
    } else {
        Write-Host "❌ Android SDK 未配置" -ForegroundColor Red
    }
    
    return @{
        Java = $javaOk
        Node = $nodeOk
        Android = $androidOk
        AllReady = ($javaOk -and $nodeOk -and $androidOk)
    }
}

# 安装 Chocolatey
function Install-Chocolatey {
    Write-Host "安装 Chocolatey 包管理器..." -ForegroundColor Yellow
    
    try {
        choco --version | Out-Null
        Write-Host "✅ Chocolatey 已安装" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "正在安装 Chocolatey..." -ForegroundColor Cyan
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        
        # 刷新环境变量
        $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH","User")
        
        Write-Host "✅ Chocolatey 安装完成" -ForegroundColor Green
        return $true
    }
}

# 快速安装所有依赖
function Install-Dependencies {
    Write-Host "安装开发环境依赖..." -ForegroundColor Yellow
    
    # 安装 Java JDK
    Write-Host "安装 Java JDK 11..." -ForegroundColor Cyan
    choco install openjdk11 -y
    
    # 安装 Node.js
    Write-Host "安装 Node.js..." -ForegroundColor Cyan
    choco install nodejs -y
    
    # 安装 Android SDK
    Write-Host "安装 Android SDK..." -ForegroundColor Cyan
    choco install android-sdk -y
    
    # 刷新环境变量
    Write-Host "刷新环境变量..." -ForegroundColor Cyan
    refreshenv
    
    # 手动设置环境变量
    $javaPath = Get-ChildItem "C:\Program Files\OpenJDK" -Directory | Select-Object -First 1
    if ($javaPath) {
        $env:JAVA_HOME = $javaPath.FullName
        [System.Environment]::SetEnvironmentVariable("JAVA_HOME", $javaPath.FullName, "Machine")
    }
    
    $androidPath = "C:\Android\android-sdk"
    if (Test-Path $androidPath) {
        $env:ANDROID_HOME = $androidPath
        $env:ANDROID_SDK_ROOT = $androidPath
        [System.Environment]::SetEnvironmentVariable("ANDROID_HOME", $androidPath, "Machine")
        [System.Environment]::SetEnvironmentVariable("ANDROID_SDK_ROOT", $androidPath, "Machine")
    }
    
    Write-Host "✅ 依赖安装完成" -ForegroundColor Green
}

# 配置 Android SDK
function Setup-AndroidSDK {
    Write-Host "配置 Android SDK..." -ForegroundColor Yellow
    
    if (-not $env:ANDROID_HOME) {
        Write-Host "❌ ANDROID_HOME 未设置" -ForegroundColor Red
        return $false
    }
    
    $sdkManager = Join-Path $env:ANDROID_HOME "tools\bin\sdkmanager.bat"
    if (-not (Test-Path $sdkManager)) {
        $sdkManager = Join-Path $env:ANDROID_HOME "cmdline-tools\latest\bin\sdkmanager.bat"
    }
    
    if (Test-Path $sdkManager) {
        Write-Host "安装 Android SDK 组件..." -ForegroundColor Cyan
        & $sdkManager "platform-tools" "platforms;android-28" "build-tools;28.0.3" --sdk_root=$env:ANDROID_HOME
        Write-Host "✅ Android SDK 配置完成" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ 找不到 sdkmanager" -ForegroundColor Red
        return $false
    }
}

# 构建 APK
function Build-APK {
    Write-Host "构建 APK 文件..." -ForegroundColor Yellow
    
    try {
        # 安装 Cordova
        Write-Host "安装 Cordova CLI..." -ForegroundColor Cyan
        npm install -g cordova
        
        # 安装项目依赖
        Write-Host "安装项目依赖..." -ForegroundColor Cyan
        npm install
        
        # 准备 Android 平台
        Write-Host "准备 Android 平台..." -ForegroundColor Cyan
        npx cordova prepare android
        
        # 构建 APK
        Write-Host "构建 APK..." -ForegroundColor Cyan
        npx cordova build android --debug --verbose
        
        # 检查 APK 文件
        $apkPath = "platforms\android\app\build\outputs\apk\debug\app-debug.apk"
        if (Test-Path $apkPath) {
            $apkSize = (Get-Item $apkPath).Length / 1MB
            Write-Host "🎉 APK 构建成功！" -ForegroundColor Green
            Write-Host "📱 APK 位置: $apkPath" -ForegroundColor Cyan
            Write-Host "📏 APK 大小: $([math]::Round($apkSize, 2)) MB" -ForegroundColor Cyan
            
            # 复制到根目录方便查找
            Copy-Item $apkPath "xiaomi-tv-webapp.apk"
            Write-Host "📋 已复制到: xiaomi-tv-webapp.apk" -ForegroundColor Cyan
            
            return $true
        } else {
            Write-Host "❌ APK 文件未生成" -ForegroundColor Red
            return $false
        }
        
    } catch {
        Write-Host "❌ 构建失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 主流程
function Main {
    Write-Host "检查当前环境..." -ForegroundColor Yellow
    $env = Test-Environment
    
    if ($env.AllReady -and -not $QuickInstall) {
        Write-Host "✅ 环境已就绪，开始构建..." -ForegroundColor Green
    } elseif (-not $SkipInstall) {
        Write-Host "需要安装开发环境..." -ForegroundColor Yellow
        
        $install = Read-Host "是否自动安装所需环境？这将安装 Java JDK, Node.js, Android SDK (y/n)"
        if ($install -eq "y" -or $install -eq "Y") {
            if (Install-Chocolatey) {
                Install-Dependencies
                Setup-AndroidSDK
            } else {
                Write-Host "❌ Chocolatey 安装失败" -ForegroundColor Red
                return
            }
        } else {
            Write-Host "请手动安装环境后重新运行脚本" -ForegroundColor Yellow
            Write-Host "参考文档: QUICK_APK_BUILD.md" -ForegroundColor Cyan
            return
        }
    }
    
    # 最终环境检查
    Write-Host "最终环境检查..." -ForegroundColor Yellow
    $finalEnv = Test-Environment
    
    if ($finalEnv.AllReady) {
        Write-Host "✅ 环境检查通过，开始构建 APK..." -ForegroundColor Green
        if (Build-APK) {
            Write-Host ""
            Write-Host "🎉 构建完成！" -ForegroundColor Green
            Write-Host "APK 文件已生成: xiaomi-tv-webapp.apk" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "安装方法:" -ForegroundColor Yellow
            Write-Host "1. 复制 APK 到 U盘，在小米电视上安装" -ForegroundColor White
            Write-Host "2. 使用 ADB: adb install xiaomi-tv-webapp.apk" -ForegroundColor White
        }
    } else {
        Write-Host "❌ 环境仍未就绪，请检查安装" -ForegroundColor Red
        Write-Host "参考文档: QUICK_APK_BUILD.md" -ForegroundColor Cyan
    }
}

# 显示使用说明
Write-Host "使用方法:" -ForegroundColor Cyan
Write-Host "  .\auto-build-apk.ps1              # 检查环境并构建" -ForegroundColor White
Write-Host "  .\auto-build-apk.ps1 -QuickInstall # 强制重新安装环境" -ForegroundColor White
Write-Host "  .\auto-build-apk.ps1 -SkipInstall  # 跳过环境安装" -ForegroundColor White
Write-Host ""

# 执行主流程
Main

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
Read-Host
