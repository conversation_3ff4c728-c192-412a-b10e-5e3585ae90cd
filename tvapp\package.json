{"name": "xiaomi-tv-webapp", "displayName": "小米电视网页应用", "version": "1.0.0", "description": "专为小米电视MIUI TV 1.3.51设计的全屏网页应用", "main": "index.js", "scripts": {"build": "<PERSON>ova build android", "build:release": "cordova build android --release", "prepare": "cordova prepare android", "clean": "cordova clean android", "serve": "cordova serve", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ecosystem:cordova", "xiaomi-tv", "miui-tv", "android-tv", "webapp", "fullscreen"], "author": "TV App Developer", "license": "MIT", "devDependencies": {"cordova-android": "^14.0.1"}, "cordova": {"platforms": ["android"]}}