{"name": "com.example.tvapp", "displayName": "TVApp", "version": "1.0.0", "description": "A sample Apache Cordova application that responds to the deviceready event.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ecosystem:cordova"], "author": "Apache Cordova Team", "license": "Apache-2.0", "devDependencies": {"cordova-android": "^14.0.1"}, "cordova": {"platforms": ["android"]}}